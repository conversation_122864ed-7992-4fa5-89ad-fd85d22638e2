!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers"),require("vest/promisify")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","vest/promisify"],r):r((e||self).hookformResolversVest={},e.hookformResolvers,e.promisify)}(this,function(e,r,o){function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var s=/*#__PURE__*/t(o),i=function(e,r){var o={};for(var t in e)o[t]||(o[t]={message:e[t][0],type:""}),r&&(o[t].types=e[t].reduce(function(e,r,o){return(e[o]=r)&&e},{}));return o};e.vestResolver=function(e,o,t){return void 0===t&&(t={}),function(o,n,f){try{var u=function(e){return e.hasErrors()?{values:{},errors:r.toNestErrors(i(e.getErrors(),!f.shouldUseNativeValidation&&"all"===f.criteriaMode),f)}:(f.shouldUseNativeValidation&&r.validateFieldsNatively({},f),{values:o,errors:{}})};return Promise.resolve("sync"===t.mode?u(e(o,f.names,n)):Promise.resolve(s.default(e)(o,f.names,n)).then(u))}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=vest.umd.js.map
