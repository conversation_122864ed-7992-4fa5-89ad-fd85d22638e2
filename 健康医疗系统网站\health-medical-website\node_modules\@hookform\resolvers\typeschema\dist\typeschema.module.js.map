{"version": 3, "file": "typeschema.module.js", "sources": ["../src/typeschema.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport type { ValidationIssue } from '@typeschema/core';\nimport { validate } from '@typeschema/main';\nimport { FieldError, FieldErrors, appendErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nconst parseErrorSchema = (\n  typeschemaErrors: ValidationIssue[],\n  validateAllFieldCriteria: boolean,\n): FieldErrors => {\n  const errors: Record<string, FieldError> = {};\n\n  for (; typeschemaErrors.length; ) {\n    const error = typeschemaErrors[0];\n\n    if (!error.path) {\n      continue;\n    }\n    const _path = error.path.join('.');\n\n    if (!errors[_path]) {\n      errors[_path] = { message: error.message, type: '' };\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[''];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        '',\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    typeschemaErrors.shift();\n  }\n\n  return errors;\n};\n\nexport const typeschemaResolver: Resolver =\n  (schema, _, resolverOptions = {}) =>\n  async (values, _, options) => {\n    const result = await validate(schema, values);\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    if (result.success) {\n      return {\n        errors: {} as FieldErrors,\n        values: resolverOptions.raw ? values : (result.data as any),\n      };\n    }\n\n    return {\n      values: {},\n      errors: toNestErrors(\n        parseErrorSchema(\n          result.issues,\n          !options.shouldUseNativeValidation && options.criteriaMode === 'all',\n        ),\n        options,\n      ),\n    };\n  };\n"], "names": ["parseErrorSchema", "typeschemaErrors", "validateAllFieldCriteria", "errors", "length", "error", "path", "_path", "join", "message", "type", "types", "messages", "appendErrors", "concat", "shift", "typeschemaResolver", "schema", "_", "resolverOptions", "values", "options", "Promise", "resolve", "validate", "then", "result", "shouldUseNativeValidation", "validateFieldsNatively", "success", "raw", "data", "toNestErrors", "issues", "criteriaMode", "e", "reject"], "mappings": "0KAMA,IAAMA,EAAmB,SACvBC,EACAC,GAIA,IAFA,IAAMC,EAAqC,CAAE,EAEtCF,EAAiBG,QAAU,CAChC,IAAMC,EAAQJ,EAAiB,GAE/B,GAAKI,EAAMC,KAAX,CAGA,IAAMC,EAAQF,EAAMC,KAAKE,KAAK,KAM9B,GAJKL,EAAOI,KACVJ,EAAOI,GAAS,CAAEE,QAASJ,EAAMI,QAASC,KAAM,KAG9CR,EAA0B,CAC5B,IAAMS,EAAQR,EAAOI,GAAOI,MACtBC,EAAWD,GAASA,EAAM,IAEhCR,EAAOI,GAASM,EACdN,EACAL,EACAC,EACA,GACAS,EACK,GAAgBE,OAAOF,EAAsBP,EAAMI,SACpDJ,EAAMI,QAEd,CAEAR,EAAiBc,OAtBjB,CAuBF,CAEA,OAAOZ,CACT,EAEaa,EACX,SAACC,EAAQC,EAAGC,GACLC,YADKD,IAAAA,IAAAA,EAAkB,CAAA,GACvBC,SAAAA,EAAQF,EAAGG,OAAWC,OAAAA,QAAAC,QACNC,EAASP,EAAQG,IAAOK,cAAvCC,GAEmE,OAAzEL,EAAQM,2BAA6BC,EAAuB,CAAE,EAAEP,GAE5DK,EAAOG,QACF,CACL1B,OAAQ,CAAiB,EACzBiB,OAAQD,EAAgBW,IAAMV,EAAUM,EAAOK,MAI5C,CACLX,OAAQ,CAAE,EACVjB,OAAQ6B,EACNhC,EACE0B,EAAOO,QACNZ,EAAQM,2BAAsD,QAAzBN,EAAQa,cAEhDb,GAEH,EACH,CAAC,MAAAc,GAAAb,OAAAA,QAAAc,OAAAD,EAAA,CAAA,CAAA"}